// import { MMKV } from 'react-native-mmkv';

// Fallback MMKV implementation for testing
const storage = {
  set: (key: string, value: any): void => {
    console.log(`MMKV.set(${key}, ${value}) - fallback mode`);
  },
  getString: (key: string): string | undefined => {
    console.log(`MMKV.getString(${key}) - fallback mode`);
    return undefined;
  },
  getBoolean: (key: string): boolean | undefined => {
    console.log(`MMKV.getBoolean(${key}) - fallback mode`);
    return undefined;
  },
  getNumber: (key: string): number | undefined => {
    console.log(`MMKV.getNumber(${key}) - fallback mode`);
    return undefined;
  },
  delete: (key: string): void => {
    console.log(`MMKV.delete(${key}) - fallback mode`);
  },
  clearAll: (): void => {
    console.log(`MMKV.clearAll() - fallback mode`);
  },
  contains: (key: string): boolean => {
    console.log(`MMKV.contains(${key}) - fallback mode`);
    return false;
  },
  getAllKeys: (): string[] => {
    console.log(`MMKV.getAllKeys() - fallback mode`);
    return [];
  }
};

export { storage };

// Helper functions for common storage operations
export const StorageService = {
  // String operations
  setString: (key: string, value: string): void => {
    storage.set(key, value);
  },

  getString: (key: string): string | undefined => {
    return storage.getString(key);
  },

  // Object operations (JSON)
  setObject: (key: string, value: any): void => {
    storage.set(key, JSON.stringify(value));
  },

  getObject: <T = any>(key: string): T | null => {
    const value = storage.getString(key);
    if (value) {
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        console.error('Failed to parse stored object:', error);
        return null;
      }
    }
    return null;
  },

  // Boolean operations
  setBoolean: (key: string, value: boolean): void => {
    storage.set(key, value);
  },

  getBoolean: (key: string): boolean | undefined => {
    return storage.getBoolean(key);
  },

  // Number operations
  setNumber: (key: string, value: number): void => {
    storage.set(key, value);
  },

  getNumber: (key: string): number | undefined => {
    return storage.getNumber(key);
  },

  // Generic operations
  remove: (key: string): void => {
    storage.delete(key);
  },

  clear: (): void => {
    storage.clearAll();
  },

  contains: (key: string): boolean => {
    return storage.contains(key);
  },

  getAllKeys: (): string[] => {
    return storage.getAllKeys();
  },
};
